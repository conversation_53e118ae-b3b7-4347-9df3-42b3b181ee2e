import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { PrismaClient } from '@prisma/client';
import EPub from 'epub';
import { GoogleGenAI } from '@google/genai';
import J<PERSON><PERSON><PERSON> from 'jszip';
import * as cheerio from 'cheerio';

const s3Client = new S3Client({ region: process.env.AWS_REGION });
const sqsClient = new SQSClient({ region: process.env.AWS_REGION });
const prisma = new PrismaClient();

if (!process.env.GEMINI_API_KEY) {
  throw new Error('FATAL: GEMINI_API_KEY is not defined in the environment.');
}
const genAI = new GoogleGenAI({});

const TONE_PROMPTS = {
  academic: `Rewrite this text in an academic tone. Use formal language, precise terminology, and scholarly structure. Maintain objectivity and include appropriate academic phrasing. Present information systematically and analytically:`,
  simplified: `Rewrite this text in simple, clear language. Use short sentences, common words, and straightforward explanations. Break down complex concepts into easy-to-understand parts:`,
  kindergarten: `Rewrite this text for young children (ages 5-6). Use very simple words, short sentences, and fun examples. Explain things like you're talking to a curious child:`,
  poetic: `Rewrite this text in a poetic style. Use beautiful, flowing language with rhythm and imagery. Include metaphors, descriptive phrases, and lyrical expressions while keeping the meaning clear:`,
  humorous: `Rewrite this text with humor and wit. Add funny observations, clever wordplay, and amusing analogies. Keep it entertaining while preserving the important information:`,
  philosophical: `Rewrite this text in a philosophical tone. Explore deeper meanings, ask thought-provoking questions, and connect ideas to broader concepts about life, existence, and human nature:`,
  sarcastic: `Rewrite this text with a sarcastic and witty tone. Use irony, clever remarks, and dry humor. Be entertaining and slightly cynical while still conveying the essential information:`,
};

export const handler = async (event) => {
  for (const record of event.Records) {
    const message = JSON.parse(record.body);
    const { fileId, s3Key, tone = 'simplified', userId } = message;

    try {
      await processFileFromS3(fileId, s3Key, tone, userId);
    } catch (error) {
      console.error('Error processing file:', error);
      await updateFileStatus(fileId, 'FAILED', null, userId);
    }
  }
};

async function processFileFromS3(fileId, s3Key, tone, userId) {
  console.log('Processing file:', s3Key);

  const downloadParams = {
    Bucket: process.env.AWS_S3_FILES_BUCKET,
    Key: s3Key,
  };

  const response = await s3Client.send(new GetObjectCommand(downloadParams));
  const fileBuffer = Buffer.from(await response.Body.transformToByteArray());

  const epub = await parseEpubFromBuffer(fileBuffer);
  const chapters = await extractAllChaptersFromBuffer(epub, fileBuffer);

  const processedChunks = await paraphraseTextWithGemini(chapters, tone);

  console.log(`✅ All chapters processed.`);
  structureResults(chapters, processedChunks);

  console.log(`✅ Processed content structured`);
  const newEpubBuffer = await createEpubBuffer(chapters, fileBuffer, epub);
  const processedKey = await uploadProcessedFile(newEpubBuffer, fileId);

  await updateFileStatus(fileId, 'COMPLETED', processedKey, userId);
  console.log(`✅ File ${fileId} processed successfully`);
}

function parseEpubFromBuffer(buffer) {
  return new Promise((resolve, reject) => {
    const epub = new EPub(buffer);
    epub.on('end', () => {
      console.log('✅ EPUB parsed successfully');
      resolve(epub);
    });
    epub.on('error', (error) => reject(error));
    epub.parse();
  });
}

async function getChapterContent(zip, href) {
  const file = zip.file(href);
  if (!file) throw new Error(`File not found in ZIP: ${href}`);
  return await file.async('string');
}

async function extractAllChaptersFromBuffer(epub, buffer) {
  console.log('📄 Extracting chapter contents...');
  const zip = await JSZip.loadAsync(buffer);
  const chapters = [];

  for (let i = 0; i < epub.flow.length; i++) {
    const chapter = epub.flow[i];
    try {
      const html = await getChapterContent(zip, chapter.href);
      const root = extractText(html);
      const chunks = await chunkText(root.nodes);
      chapters.push({
        html: html,
        title: chapter.title,
        order: i + 1,
        chunks: chunks,
        root: root,
      });
    } catch (error) {
      console.error(`   ❌ Error processing chapter ${i + 1}:`, error);
    }
  }
  return chapters.filter((chapter) => chapter !== null);
}

function extractText(htmlContent) {
  const $ = cheerio.load(htmlContent);
  const nodes = [];
  function walk(node) {
    $(node)
      .contents()
      .each((_, child) => {
        if (child.type === 'text') {
          const text = $(child).text().trim();
          if (text) nodes.push({ node: child, original: text });
        } else if (child.type === 'tag') {
          walk(child);
        }
      });
  }
  walk($('body'));
  return { $, nodes };
}

function estimateTokens(text) {
  return Math.ceil(text.length / 4);
}

async function chunkText(
  textElements,
  maxTokens = 15000,
  maxArrayLength = 150
) {
  const chunks = [];
  let currentChunkList = [];
  let currentChunk = '';

  for (const element of textElements) {
    const testChunk = currentChunk + element.original;
    const totalTokens = estimateTokens(testChunk);

    if (
      (totalTokens > maxTokens || currentChunkList.length >= maxArrayLength) &&
      currentChunk.length > 0
    ) {
      chunks.push([...currentChunkList]);
      currentChunk = element.original;
      currentChunkList = [element.original];
    } else {
      currentChunkList.push(element.original);
      currentChunk = testChunk;
    }
  }

  if (currentChunkList.length > 0) {
    chunks.push(currentChunkList);
  }

  return chunks;
}

async function paraphraseTextWithGemini(chapters, tone = 'simplified') {
  const tonePrompt = TONE_PROMPTS[tone] || TONE_PROMPTS.simplified;

  const reassembledResults = chapters.map((ch) =>
    ch.chunks.map((chunk) => Array(chunk.length).fill(null))
  );

  let workQueue = [];
  chapters.forEach((chapter, originalChapterIndex) => {
    chapter.chunks.forEach((chunk, originalChunkIndex) => {
      workQueue.push({
        chunk,
        originalChapterIndex,
        originalChunkIndex,
        startIndexInChunk: 0,
      });
    });
  });

  if (workQueue.length === 0) {
    console.log('No text chunks to process.');
    return [];
  }

  const BATCH_SIZE = 15;
  console.log(
    `🚀 Processing ${workQueue.length} initial chunks in batches of up to ${BATCH_SIZE}...`
  );

  while (workQueue.length > 0) {
    const batchJobs = workQueue.splice(0, BATCH_SIZE);
    const batchStartTime = Date.now();

    console.log(
      `   - Starting batch... (${batchJobs.length} jobs in this batch, ${workQueue.length} remaining in queue)`
    );

    const batchPromises = batchJobs.map((job) =>
      paraphraseChunkWithRetry(job.chunk, tonePrompt)
        .then((processedChunk) => ({
          status: 'fulfilled',
          value: processedChunk,
          job,
        }))
        .catch((error) => ({ status: 'rejected', reason: error, job }))
    );

    const batchResults = await Promise.all(batchPromises);

    const highPriorityJobs = [];
    for (const result of batchResults) {
      const { job } = result;

      if (result.status === 'fulfilled') {
        result.value.forEach((rewrittenText, i) => {
          const finalIndex = job.startIndexInChunk + i;
          reassembledResults[job.originalChapterIndex][job.originalChunkIndex][
            finalIndex
          ] = rewrittenText;
        });
      } else {
        console.warn(
          `   - Chunk from Chap ${job.originalChapterIndex}, Chunk ${job.originalChunkIndex} failed. Error: ${result.reason.message}`
        );
        const originalChunk = job.chunk;

        if (originalChunk.length > 1) {
          console.log(
            `   - Splitting failed chunk and re-queuing with high priority.`
          );
          const midPoint = Math.ceil(originalChunk.length / 2);
          const firstHalf = originalChunk.slice(0, midPoint);
          const secondHalf = originalChunk.slice(midPoint);

          highPriorityJobs.push({
            ...job,
            chunk: firstHalf,
          });
          highPriorityJobs.push({
            ...job,
            chunk: secondHalf,
            startIndexInChunk: job.startIndexInChunk + midPoint,
          });
        } else {
          console.error(
            `   - Cannot split chunk further. Falling back to original text.`
          );
          const originalText = job.chunk[0];
          reassembledResults[job.originalChapterIndex][job.originalChunkIndex][
            job.startIndexInChunk
          ] = originalText;
        }
      }
    }

    if (highPriorityJobs.length > 0) {
      workQueue.unshift(...highPriorityJobs);
      console.log(
        `   - Re-queued ${highPriorityJobs.length} split sub-chunks with high priority.`
      );
    }

    const batchDuration = Date.now() - batchStartTime;
    if (workQueue.length > 0) {
      const timeToWait = 60000 - batchDuration;
      if (timeToWait > 0) {
        console.log(
          `   - Batch finished in ${(batchDuration / 1000).toFixed(
            2
          )}s. Waiting for ${(timeToWait / 1000).toFixed(2)}s.`
        );
        await new Promise((resolve) => setTimeout(resolve, timeToWait));
      } else {
        console.log(
          `   - Batch finished in ${(batchDuration / 1000).toFixed(
            2
          )}s. Proceeding immediately.`
        );
      }
    } else {
      console.log(
        `   - Final batch finished in ${(batchDuration / 1000).toFixed(2)}s.`
      );
    }
  }

  console.log('✅ All processing complete. Verifying final structure...');
  chapters.forEach((chapter, chapterIndex) => {
    chapter.chunks.forEach((chunk, chunkIndex) => {
      chunk.forEach((originalText, textIndex) => {
        if (reassembledResults[chapterIndex][chunkIndex][textIndex] === null) {
          console.warn(
            `   - Found null value at [${chapterIndex}][${chunkIndex}][${textIndex}]. Falling back to original text.`
          );
          reassembledResults[chapterIndex][chunkIndex][textIndex] =
            originalText;
        }
      });
    });
  });

  return reassembledResults;
}

const MAX_RETRY_ATTEMPTS = 1;

async function paraphraseChunkWithRetry(chunk, tonePrompt) {
  const fullPrompt = `${tonePrompt}\n\nYou will receive a JSON array of text strings. Your task is to rewrite each string individually while keeping the exact same array structure.\n\nINPUT ARRAY:\n${JSON.stringify(
    chunk,
    null,
    2
  )}\n\nRULES:\n1. Rewrite each array element separately (do not combine them).\n2. Keep the same number of elements in the array. The output array length MUST match the input array length.\n3. Each element should be the rewritten version of the corresponding input element.\n4. The output must be a valid JSON array containing exactly the same number of elements as the input array.\n5. Do not merge multiple elements into one.\n6. Preserve special characters and the original casing of the text.\n7. Do not encode or escape HTML entities like &nbsp; or &quot;. Keep them exactly as-is.\n\nDo NOT include any explanatory text, comments, markdown, or any characters outside of the final JSON array.`;

  for (let attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
    try {
      const response = await genAI.models.generateContent({
        model: 'gemini-2.5-flash-lite',
        config: {
          responseMimeType: 'application/json',
        },
        contents: fullPrompt,
      });

      const responseText = response.text || '';
      const parsedArray = JSON.parse(responseText);

      if (Array.isArray(parsedArray) && parsedArray.length === chunk.length) {
        console.log(
          `   - Successfully paraphrased chunk with ${chunk.length} elements.`
        );
        return parsedArray;
      } else {
        throw new Error(
          `Structure mismatch: expected ${chunk.length}, got ${parsedArray.length}`
        );
      }
    } catch (error) {
      const errorMessage = error.message;
      console.warn(
        `   - Attempt ${attempt} failed for chunk of size ${chunk.length}. Error: ${errorMessage}`
      );

      if (attempt === MAX_RETRY_ATTEMPTS) {
        throw new Error(
          `Chunk failed after ${MAX_RETRY_ATTEMPTS} attempts. Error: ${errorMessage}`
        );
      }
      const delay = Math.pow(2, attempt) * 1000;
      console.log(`   - Waiting for ${delay / 1000}s before retrying...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
  throw new Error('All retry attempts failed for an unknown reason.');
}

function structureResults(chapters, paraphrasedChunks) {
  chapters.forEach((ch, i) => {
    if (!paraphrasedChunks[i] || paraphrasedChunks[i].length === 0) return;
    const flatChunks = paraphrasedChunks[i].flat(Infinity);
    ch.root.nodes.forEach(({ node }, y) => {
      const text = flatChunks[y];
      if (text !== undefined) {
        node.data = text + ' ';
      }
    });
  });
}

async function createEpubBuffer(chapters, originalBuffer, epub) {
  const originalZip = await JSZip.loadAsync(originalBuffer);

  const updatedChapters = {};
  epub.flow.forEach(
    (chapter, i) => (updatedChapters[chapter.href] = chapters[i].root.$.html())
  );

  for (const [href, newContent] of Object.entries(updatedChapters)) {
    if (originalZip.file(href)) {
      originalZip.file(href, newContent);
    } else {
      console.warn(`⚠️ File not found in original EPUB: ${href}`);
    }
  }

  return await originalZip.generateAsync({
    type: 'nodebuffer',
    mimeType: 'application/epub+zip',
    compression: 'DEFLATE',
    compressionOptions: { level: 9 },
  });
}

async function uploadProcessedFile(buffer, fileId) {
  const processedKey = `processed/${fileId}-${Date.now()}.epub`;

  await s3Client.send(
    new PutObjectCommand({
      Bucket: process.env.AWS_S3_FILES_BUCKET,
      Key: processedKey,
      Body: buffer,
      ContentType: 'application/epub+zip',
    })
  );

  return processedKey;
}

async function updateFileStatus(fileId, status, processedKey = null, userId) {
  const updateData = { status };
  if (processedKey) updateData.processedFileKey = processedKey;

  await prisma.file.update({
    where: { id: fileId },
    data: updateData,
  });

  // Broadcast status update if you have websocket functionality
  if (typeof broadcastFileStatusUpdate === 'function') {
    broadcastFileStatusUpdate(userId, {
      id: fileId,
      status: status,
    });
  }
}
